<?php

namespace App\Http\Controllers;

use App\Models\Purchase;
use App\Models\Product;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use App\Helpers\BankAccountHelper;

class PurchaseController extends Controller
{
    public function index(Request $request)
    {
        try {
            $query = Purchase::with(['supplier', 'store', 'items']);
            if ($request->has('fromDate') && $request->has('toDate')) {
                $query->whereBetween('date_of_purchase', [$request->fromDate, $request->toDate]);
            }
            $purchases = $query->get();
            return response()->json(['success' => true, 'data' => $purchases], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching purchases: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching purchases'], 500);
        }
    }

    public function store(Request $request)
    {
        Log::info('Creating new purchase:', $request->all());

        try {
            // Validate request data
            $validatedData = $request->validate($this->validationRules());

            DB::beginTransaction();

            // Use the total from frontend if provided, otherwise calculate it
            if (!isset($validatedData['total']) || $validatedData['total'] <= 0) {
                // Calculate total excluding free items
                $itemsTotal = 0;
                foreach ($validatedData['items'] as $item) {
                    $subtotal = $item['quantity'] * $item['buying_cost'];
                    $discountAmount = $item['discount_amount'] ?? 0;
                    $itemsTotal += $subtotal - $discountAmount;
                }

                // Apply overall purchase discount and tax to get final total
                $purchaseDiscountAmount = $validatedData['discount_amount'] ?? 0;
                $taxAmount = $validatedData['tax'] ?? 0;
                $total = $itemsTotal - $purchaseDiscountAmount + $taxAmount;
                $validatedData['total'] = $total;
            }

            // Generate bill number if not provided
            Log::info('Bill number generation - received bill_number:', ['bill_number' => $validatedData['bill_number'] ?? 'NULL']);
            
            if (empty($validatedData['bill_number'])) {
                // Find the highest existing bill number
                $latestPurchase = Purchase::orderBy('id', 'desc')->first();
                $nextNumber = 1;
                
                if ($latestPurchase) {
                    // Extract the number from the latest bill number
                    $latestNumber = (int)preg_replace('/[^0-9]/', '', $latestPurchase->bill_number);
                    $nextNumber = $latestNumber + 1;
                    Log::info('Latest purchase found:', ['latest_bill_number' => $latestPurchase->bill_number, 'next_number' => $nextNumber]);
                } else {
                    Log::info('No existing purchases found, starting with number 1');
                }
                
                // Generate the new bill number
                $newBillNumber = 'GRN-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
                Log::info('Generated new bill number:', ['new_bill_number' => $newBillNumber]);
                
                // Check if this bill number already exists (in case of gaps or concurrent requests)
                while (Purchase::withTrashed()->where('bill_number', $newBillNumber)->exists()) {
                    $nextNumber++;
                    $newBillNumber = 'GRN-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
                    Log::info('Bill number exists (including soft-deleted), trying next:', ['new_bill_number' => $newBillNumber]);
                }
                
                $validatedData['bill_number'] = $newBillNumber;
                Log::info('Final bill number assigned:', ['final_bill_number' => $newBillNumber]);
            } else {
                // Check if the provided bill number is unique (including soft-deleted)
                $existingPurchase = Purchase::withTrashed()->where('bill_number', $validatedData['bill_number'])->first();
                if ($existingPurchase) {
                    if ($existingPurchase->trashed()) {
                        // If soft-deleted, force delete it to free up the bill number
                        $existingPurchase->forceDelete();
                        Log::warning('Soft-deleted purchase with duplicate bill number force deleted:', ['bill_number' => $validatedData['bill_number']]);
                    } else {
                        Log::error('Bill number already exists (active):', ['bill_number' => $validatedData['bill_number']]);
                        throw new \Exception('Bill number already exists');
                    }
                }
                Log::info('Using provided bill number:', ['bill_number' => $validatedData['bill_number']]);
            }

            $purchase = Purchase::create([
                'bill_number' => $validatedData['bill_number'],
                'invoice_number' => $validatedData['invoice_number'],
                'date_of_purchase' => $validatedData['date_of_purchase'],
                'payment_method' => $validatedData['payment_method'],
                'supplier_id' => $validatedData['supplier_id'],
                'store_id' => $validatedData['store_id'],
                'paid_amount' => $validatedData['paid_amount'],
                'discount_percentage' => $validatedData['discount_percentage'],
                'discount_amount' => $validatedData['discount_amount'],
                'tax' => $validatedData['tax'],
                'status' => $validatedData['status'],
                'total' => $validatedData['total'],
                'cheque_no' => $validatedData['cheque_no'] ?? null,
                'bank_name' => $validatedData['bank_name'] ?? null,
                'issue_date' => $validatedData['issue_date'] ?? null,
                'bank' => $validatedData['bank'] ?? null,
            ]);

            foreach ($validatedData['items'] as $item) {
                Log::info('Processing purchase item:', [
                    'item_data' => $item,
                    'product_variant_id' => $item['variant_id'] ?? 'NULL',
                    'batch_number' => $item['batch_number'] ?? 'NULL',
                    'expiry_date' => $item['expiry_date'] ?? 'NULL'
                ]);

                // Prepare expiry date - handle invalid formats
                $expiryDate = null;
                if (!empty($item['expiry_date']) && $item['expiry_date'] !== 'N/A') {
                    try {
                        $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                        if ($expiryDate === '1970-01-01' || !$expiryDate) {
                            $expiryDate = null;
                        }
                    } catch (\Exception $e) {
                        $expiryDate = null;
                    }
                }

                // Handle product lookup
                $product = null;
                if (isset($item['product_id'])) {
                    $product = Product::find($item['product_id']);
                } else {
                    $product = Product::where('product_name', $item['product_name'])->first();
                }

                if ($product) {
                    \App\Models\PurchaseItem::create([
                        'purchase_id' => $purchase->id,
                        'product_id' => $product->product_id,
                        'product_variant_id' => $item['variant_id'] ?? null,
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $expiryDate,
                        'quantity' => $item['quantity'],
                        'free_items' => $item['free_items'] ?? 0,
                        'buying_cost' => $item['buying_cost'],
                        'discount_percentage' => $item['discount_percentage'] ?? 0,
                        'discount_amount' => $item['discount_amount'] ?? 0,
                    ]);
                }
            }

            // Save payment details
            $supplier = \App\Models\Supplier::find($validatedData['supplier_id']);
            PaymentMethod::create([
                'type' => 'Purchase',
                'reference_number' => $purchase->bill_number,
                'refer_type' => 'Supplier',
                'refer_id' => $purchase->supplier_id,
                'refer_name' => $supplier ? $supplier->supplier_name : 'Unknown Supplier',
                'total' => $purchase->total,
                'payment_type' => $purchase->payment_method,
                'cheque_no' => $validatedData['cheque_no'] ?? null,
                'bank_name' => $validatedData['bank_name'] ?? null,
                'issue_date' => $validatedData['issue_date'] ?? null,
                'bank' => BankAccountHelper::getBankAccountName($validatedData['bank'] ?? null),
                'settled_amount' => $purchase->paid_amount,
                'balance_amount' => $purchase->total - $purchase->paid_amount,
                'date' => $purchase->date_of_purchase,
            ]);

            // Create bank account ledger entry for online, card, and cheque payments
            if (in_array(strtolower($purchase->payment_method), ['online', 'card', 'cheque']) &&
                !empty($validatedData['bank']) &&
                $purchase->paid_amount > 0) {

                $bankAccountInfo = $validatedData['bank'];
                $bankAccountParts = explode('-', $bankAccountInfo);

                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];

                    // Create payment entry for bank account ledger (credit for purchase)
                    \App\Models\Payment::create([
                        'voucher_no' => $purchase->bill_number . '-BANK',
                        'transaction_id' => $purchase->id,
                        'transaction_type' => 'purchase_bank',
                        'reference_no' => $purchase->bill_number,
                        'refer_type' => $bankAccountType === 'staff_ledger' ? 'Ledger' : 'SubGroup',
                        'refer_id' => $bankAccountId,
                        'refer_name' => $bankAccountType === 'staff_ledger' ?
                            (\App\Models\StaffLedger::find($bankAccountId)->name ?? 'Unknown Bank Account') :
                            (\App\Models\AccountSubGroup::find($bankAccountId)->sub_group_name ?? 'Unknown Bank Account'),
                        'amount' => -$purchase->paid_amount, // Negative for credit entry
                        'discount' => 0,
                        'payment_date' => $purchase->date_of_purchase,
                        'payment_method' => ucfirst($purchase->payment_method),
                        'bank' => $bankAccountInfo,
                        'note' => 'Purchase payment - Bank account credit entry',
                        'account_type' => 'Bank Accounts',
                    ]);
                }
            }

            if (strtolower($validatedData['payment_method'] ?? '') === 'cheque' && ($validatedData['paid_amount'] ?? 0) > 0) {
                // Delete existing payment for this purchase if any
                \App\Models\Payment::where('transaction_type', 'purchase')
                    ->where('transaction_id', $purchase->id)
                    ->delete();

                $payment = \App\Models\Payment::create([
                    'voucher_no' => $purchase->bill_number . '-CHQ',
                    'transaction_id' => $purchase->id,
                    'transaction_type' => 'purchase',
                    'reference_no' => $purchase->bill_number,
                    'refer_type' => 'Supplier',
                    'refer_id' => $purchase->supplier_id,
                    'refer_name' => $supplier ? $supplier->supplier_name : 'Unknown Supplier',
                    'amount' => $purchase->paid_amount,
                    'discount' => 0, // Add required discount field
                    'payment_date' => $purchase->date_of_purchase,
                    'payment_method' => 'Cheque',
                    'cheque_no' => $validatedData['cheque_no'] ?? null,
                    'bank_name' => $validatedData['bank_name'] ?? null,
                    'issue_date' => $validatedData['issue_date'] ?? null,
                    'note' => 'Purchase payment via cheque',
                ]);
                \App\Models\ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending',
                ]);
            }

            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Created',
                    'Purchases',
                    $purchase->id,
                    [
                        'bill_number' => $purchase->bill_number,
                        'supplier_id' => $purchase->supplier_id,
                        'total_amount' => $purchase->total,
                        'payment_method' => $purchase->payment_method
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Created - no authenticated user found');
            }

            return response()->json([
                'success' => true,
                'message' => 'Purchase created successfully',
                'data' => $purchase->load(['supplier', 'store', 'items']),
            ], 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            Log::warning('Validation error creating purchase:', ['errors' => $e->errors()]);
            return response()->json(['success' => false, 'message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating purchase: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating purchase', 'error' => $e->getMessage()], 500);
        }
    }

    public function update(Request $request, $id)
    {
        Log::info("Updating purchase ID: $id", $request->all());

        try {
            $validatedData = $request->validate($this->validationRules($id));

            DB::beginTransaction();

            // Use the total from frontend if provided, otherwise calculate it
            if (!isset($validatedData['total']) || $validatedData['total'] <= 0) {
                // Calculate total excluding free items
                $itemsTotal = 0;
                foreach ($validatedData['items'] as $item) {
                    $subtotal = $item['quantity'] * $item['buying_cost'];
                    $discountAmount = $item['discount_amount'] ?? 0;
                    $itemsTotal += $subtotal - $discountAmount;
                }

                // Apply overall purchase discount and tax to get final total
                $purchaseDiscountAmount = $validatedData['discount_amount'] ?? 0;
                $taxAmount = $validatedData['tax'] ?? 0;
                $total = $itemsTotal - $purchaseDiscountAmount + $taxAmount;
                $validatedData['total'] = $total;
            }

            $purchase = Purchase::findOrFail($id);

            // Check if the provided bill number is unique (excluding the current purchase)
            if (!empty($validatedData['bill_number'])) {
                $existingPurchase = Purchase::where('bill_number', $validatedData['bill_number'])
                    ->where('id', '!=', $id)
                    ->first();
                if ($existingPurchase) {
                    throw new \Exception('Bill number already exists');
                }
            } else {
                $latestPurchase = Purchase::where('id', '!=', $id)
                    ->orderBy('id', 'desc')
                    ->first();
                $nextNumber = 1;
                
                if ($latestPurchase) {
                    // Extract the number from the latest bill number
                    $latestNumber = (int)preg_replace('/[^0-9]/', '', $latestPurchase->bill_number);
                    $nextNumber = $latestNumber + 1;
                }
                
                // Generate the new bill number
                $newBillNumber = 'GRN-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
                
                // Check if this bill number already exists (in case of gaps or concurrent requests)
                while (Purchase::where('bill_number', $newBillNumber)->where('id', '!=', $id)->exists()) {
                    $nextNumber++;
                    $newBillNumber = 'GRN-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
                }
                
                $validatedData['bill_number'] = $newBillNumber;
            }

            $purchase->update([
                'bill_number' => $validatedData['bill_number'],
                'invoice_number' => $validatedData['invoice_number'],
                'date_of_purchase' => $validatedData['date_of_purchase'],
                'payment_method' => $validatedData['payment_method'],
                'supplier_id' => $validatedData['supplier_id'],
                'store_id' => $validatedData['store_id'],
                'paid_amount' => $validatedData['paid_amount'],
                'discount_percentage' => $validatedData['discount_percentage'],
                'discount_amount' => $validatedData['discount_amount'],
                'tax' => $validatedData['tax'],
                'status' => $validatedData['status'],
                'total' => $validatedData['total'],
                'cheque_no' => $validatedData['cheque_no'] ?? null,
                'bank_name' => $validatedData['bank_name'] ?? null,
                'issue_date' => $validatedData['issue_date'] ?? null,
            ]);

            $purchase->items()->delete();

            foreach ($validatedData['items'] as $item) {
                $expiryDate = null;
                if (!empty($item['expiry_date']) && $item['expiry_date'] !== 'N/A') {
                    try {
                        $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                        if ($expiryDate === '1970-01-01' || !$expiryDate) {
                            $expiryDate = null;
                        }
                    } catch (\Exception $e) {
                        $expiryDate = null;
                    }
                }

                $purchaseItemData = [
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'free_items' => $item['free_items'] ?? 0,
                    'buying_cost' => $item['buying_cost'],
                    'discount_percentage' => $item['discount_percentage'] ?? 0,
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'product_variant_id' => $item['variant_id'] ?? null,
                    'batch_number' => $item['batch_number'] ?? null,
                    'expiry_date' => $expiryDate,
                ];

                $purchase->items()->create($purchaseItemData);
            }

            // Update payment details
            $supplier = \App\Models\Supplier::find($validatedData['supplier_id']);
            PaymentMethod::updateOrCreate(
                [
                    'type' => 'Purchase',
                    'reference_number' => $purchase->bill_number,
                ],
                [
                    'refer_type' => 'Supplier',
                    'refer_id' => $purchase->supplier_id,
                    'refer_name' => $supplier ? $supplier->supplier_name : 'Unknown Supplier',
                    'total' => $purchase->total,
                    'payment_type' => $purchase->payment_method,
                    'cheque_no' => $purchase->cheque_no ?? null,
                    'bank_name' => $purchase->bank_name ?? null,
                    'issue_date' => $purchase->issue_date ?? null,
                    'bank' => BankAccountHelper::getBankAccountName($validatedData['bank'] ?? null),
                    'settled_amount' => $purchase->paid_amount,
                    'balance_amount' => $purchase->total - $purchase->paid_amount,
                    'date' => $purchase->date_of_purchase,
                ]
            );

            // Delete existing bank account ledger entries for this purchase
            \App\Models\Payment::where('transaction_type', 'purchase_bank')
                ->where('transaction_id', $purchase->id)
                ->delete();

            // Create bank account ledger entry for online, card, and cheque payments (update)
            if (in_array(strtolower($purchase->payment_method), ['online', 'card', 'cheque']) &&
                !empty($validatedData['bank']) &&
                $purchase->paid_amount > 0) {

                $bankAccountInfo = $validatedData['bank'];
                $bankAccountParts = explode('-', $bankAccountInfo);

                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];

                    // Create payment entry for bank account ledger (credit for purchase)
                    \App\Models\Payment::create([
                        'voucher_no' => $purchase->bill_number . '-BANK',
                        'transaction_id' => $purchase->id,
                        'transaction_type' => 'purchase_bank',
                        'reference_no' => $purchase->bill_number,
                        'refer_type' => $bankAccountType === 'staff_ledger' ? 'Ledger' : 'SubGroup',
                        'refer_id' => $bankAccountId,
                        'refer_name' => $bankAccountType === 'staff_ledger' ?
                            (\App\Models\StaffLedger::find($bankAccountId)->name ?? 'Unknown Bank Account') :
                            (\App\Models\AccountSubGroup::find($bankAccountId)->sub_group_name ?? 'Unknown Bank Account'),
                        'amount' => -$purchase->paid_amount, // Negative for credit entry
                        'discount' => 0,
                        'payment_date' => $purchase->date_of_purchase,
                        'payment_method' => ucfirst($purchase->payment_method),
                        'bank' => $bankAccountInfo,
                        'note' => 'Purchase payment - Bank account credit entry (updated)',
                        'account_type' => 'Bank Accounts',
                    ]);
                }
            }

            if (strtolower($validatedData['payment_method'] ?? '') === 'cheque' && ($purchase->paid_amount ?? 0) > 0) {
                // Delete existing cheque statement and payment for this purchase if any
                \App\Models\ChequeStatement::where('voucher_no', $purchase->bill_number)->delete();
                \App\Models\Payment::where('transaction_type', 'purchase')
                    ->where('transaction_id', $purchase->id)
                    ->delete();

                $payment = \App\Models\Payment::create([
                    'voucher_no' => $purchase->bill_number . '-CHQ',
                    'transaction_id' => $purchase->id,
                    'transaction_type' => 'purchase',
                    'reference_no' => $purchase->bill_number,
                    'refer_type' => 'Supplier',
                    'refer_id' => $purchase->supplier_id,
                    'refer_name' => $supplier ? $supplier->supplier_name : 'Unknown Supplier',
                    'amount' => $purchase->paid_amount,
                    'discount' => 0, // Add required discount field
                    'payment_date' => $purchase->date_of_purchase,
                    'payment_method' => 'Cheque',
                    'cheque_no' => $purchase->cheque_no ?? null,
                    'bank_name' => $purchase->bank_name ?? null,
                    'issue_date' => $purchase->issue_date ?? null,
                    'note' => 'Purchase payment via cheque',
                ]);
                \App\Models\ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending',
                ]);
            } else {
                // If payment type is not cheque, delete any existing cheque statement
                \App\Models\ChequeStatement::where('voucher_no', $purchase->bill_number)->delete();
            }

            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Updated',
                    'Purchases',
                    $purchase->id,
                    [
                        'bill_number' => $purchase->bill_number,
                        'supplier_id' => $purchase->supplier_id,
                        'total_amount' => $purchase->total,
                        'payment_method' => $purchase->payment_method
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Updated - no authenticated user found');
            }

            return response()->json([
                'success' => true,
                'message' => 'Purchase updated successfully',
                'data' => $purchase->load(['supplier', 'store', 'items']),
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollBack();
            Log::warning('Validation error updating purchase:', ['errors' => $e->errors()]);
            return response()->json(['success' => false, 'message' => 'Validation error', 'errors' => $e->errors()], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating purchase: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating purchase', 'error' => $e->getMessage()], 500);
        }
    }

    public function show($id)
    {
        try {
            $purchase = Purchase::with(['supplier', 'store', 'items'])->findOrFail($id);
            return response()->json(['success' => true, 'data' => $purchase], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching purchase: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Purchase not found'], 404);
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $purchase = Purchase::findOrFail($id);

            DB::beginTransaction();

            PaymentMethod::where('type', 'Purchase')
                ->where('reference_number', $purchase->bill_number)
                ->delete();

            $purchase->items()->delete();
            // Soft delete purchase items (they will be restored with the purchase)
            foreach ($purchase->items as $item) {
                $item->deleted_by = $request->user()->id ?? auth()->id();
                $item->save();
                $item->delete();
            }
            // Soft delete the purchase
            $purchase->deleted_by = $request->user()->id ?? auth()->id();
            $purchase->save();

            $purchase->delete();

            DB::commit();
            
            // Log user activity AFTER commit
            $deletedBy = $request->user()->id ?? auth()->id();
            if ($deletedBy) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $deletedBy,
                    'Purchase Deleted',
                    'Purchases',
                    $purchase->id,
                    [
                        'bill_number' => $purchase->bill_number,
                        'supplier_id' => $purchase->supplier_id,
                        'total_amount' => $purchase->total,
                        'deleted_by' => $deletedBy
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Deleted - no authenticated user found');
            }

            return response()->json(['success' => true, 'message' => 'Purchase and all items moved to trash'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting purchase: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error deleting purchase', 'error' => $e->getMessage()], 500);
        }
    }

    public function getDeletedPurchases()
    {
        $purchases = Purchase::onlyTrashed()->with(['itemsWithTrashed.product' => function($query) {
            $query->withTrashed();
        }, 'deletedByUser'])->get();
        
        // Transform the purchases to include items in the expected format and deleted_by_user
        $transformedPurchases = $purchases->map(function($purchase) {
            $purchaseData = $purchase->toArray();
            
            // Add supplier name
            $supplier = \App\Models\Supplier::find($purchase->supplier_id);
            $purchaseData['supplier_name'] = $supplier ? $supplier->supplier_name : 'Unknown Supplier';
            
            // Transform items
            $purchaseData['items'] = $purchase->itemsWithTrashed->map(function($item) {
                return [
                    'id' => $item->id,
                    'purchase_id' => $item->purchase_id,
                    'product_id' => $item->product_id,
                    'product_name' => $item->product_name,
                    'quantity' => $item->quantity,
                    'free_items' => $item->free_items,
                    'buying_cost' => $item->buying_cost,
                    'discount_percentage' => $item->discount_percentage,
                    'discount_amount' => $item->discount_amount,
                    'total' => $item->total,
                    'batch_number' => $item->batch_number,
                    'expiry_date' => $item->expiry_date,
                    'product' => $item->product ? [
                        'id' => $item->product->id,
                        'product_name' => $item->product->product_name,
                        'item_code' => $item->product->item_code,
                    ] : null,
                    'created_at' => $item->created_at,
                    'updated_at' => $item->updated_at,
                    'deleted_at' => $item->deleted_at,
                ];
            })->toArray();
            $purchaseData['deleted_by_user'] = $purchase->deletedByUser ? [
                'id' => $purchase->deletedByUser->id,
                'name' => $purchase->deletedByUser->name,
                'email' => $purchase->deletedByUser->email,
            ] : null;
            return $purchaseData;
        });
        
        return response()->json([
            'data' => $transformedPurchases,
            'meta' => [
                'current_page' => 1,
                'last_page' => 1,
                'per_page' => $purchases->count(),
                'total' => $purchases->count(),
            ]
        ]);
    }

    public function restorePurchase($id)
    {
        try {
            DB::beginTransaction();
            
            // Restore the main purchase
            $purchase = Purchase::onlyTrashed()->findOrFail($id);
            $purchase->restore();
            
            // Restore all associated purchase items
            $purchase->itemsWithTrashed()->restore();
            
            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Restored',
                    'Purchases',
                    $purchase->id,
                    [
                        'bill_number' => $purchase->bill_number,
                        'supplier_id' => $purchase->supplier_id,
                        'total_amount' => $purchase->total,
                        'restored_by' => $userId
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Restored - no authenticated user found');
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Purchase and all items restored successfully',
                'data' => $purchase->load(['items', 'supplier'])
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error restoring purchase: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore purchase: ' . $e->getMessage()
            ], 500);
        }
    }

    public function forceDeletePurchase($id)
    {
        try {
            DB::beginTransaction();
            
            $purchase = Purchase::onlyTrashed()->findOrFail($id);
            
            // Force delete all associated purchase items
            $purchase->itemsWithTrashed()->forceDelete();
            
            // Force delete the purchase
            $purchase->forceDelete();
            
            DB::commit();
            
            // Log user activity AFTER commit
            $userId = auth()->id();
            if ($userId) {
                \App\Http\Controllers\UserActivityController::logActivity(
                    $userId,
                    'Purchase Permanently Deleted',
                    'Purchases',
                    $purchase->id,
                    [
                        'bill_number' => $purchase->bill_number,
                        'supplier_id' => $purchase->supplier_id,
                        'total_amount' => $purchase->total,
                        'permanently_deleted_by' => $userId
                    ]
                );
            } else {
                \Log::error('Cannot log Purchase Permanently Deleted - no authenticated user found');
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Purchase and all items permanently deleted'
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error force deleting purchase: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to permanently delete purchase: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getPurchaseItems($id)
    {
        try {
            $purchase = Purchase::withTrashed()->findOrFail($id);
            
            // Get purchase items with product information (including trashed products)
            $items = $purchase->itemsWithTrashed()->with(['product' => function($query) {
                $query->withTrashed();
            }])->get();
            
            return response()->json([
                'success' => true,
                'items' => $items
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching purchase items: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch purchase items'
            ], 500);
        }
    }

    private function validationRules($id = null)
    {
        return [
            'bill_number' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('purchases', 'bill_number')->ignore($id),
            ],
            'invoice_number' => 'required|string|max:255',
            'date_of_purchase' => 'required|date',
            'payment_method' => 'required|string|in:Cash,Credit,Cheque,Card,Online',
            'supplier_id' => 'required|exists:suppliers,id',
            'store_id' => 'required|exists:store_locations,id',
            'paid_amount' => [
                'required',
                'numeric',
                'min:0',
                // Custom validation for Cheque payment
                function ($attribute, $value, $fail) use ($id) {
                    $total = request()->input('total', 0);
                    if (request()->input('payment_method') === 'Cheque') {
                        if ($value > $total) {
                            $fail("The paid amount cannot exceed the total amount ($total) for Cheque payment.");
                        }
                    }
                },
            ],
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'tax' => 'nullable|numeric|min:0',
            'status' => 'required|in:pending,paid,cancelled',
            'total' => 'required|numeric|min:0',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,product_id',
            'items.*.variant_id' => 'nullable|integer',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|string',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.free_items' => 'nullable|integer|min:0',
            'items.*.buying_cost' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            // Cheque-specific fields
            'cheque_no' => 'required_if:payment_method,Cheque|string|nullable',
            'bank_name' => 'required_if:payment_method,Cheque|string|nullable',
            'issue_date' => 'required_if:payment_method,Cheque|date|nullable',
            'bank' => 'nullable|string|max:255',
        ];
    }
}