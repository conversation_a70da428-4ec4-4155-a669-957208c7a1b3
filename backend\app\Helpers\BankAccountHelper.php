<?php

namespace App\Helpers;

use App\Models\StaffLedger;
use App\Models\AccountSubGroup;

class BankAccountHelper
{
    /**
     * Extract bank account name from the format "staff_ledger-{id}" or "sub_group-{id}"
     * 
     * @param string|null $bankField The bank field value in format "staff_ledger-{id}" or "sub_group-{id}"
     * @return string|null The actual bank account name or null if not found
     */
    public static function getBankAccountName($bankField)
    {
        if (empty($bankField)) {
            return null;
        }

        // If the bank field doesn't contain a dash, assume it's already a name
        if (strpos($bankField, '-') === false) {
            return $bankField;
        }

        $bankAccountParts = explode('-', $bankField);
        
        if (count($bankAccountParts) !== 2) {
            return $bankField; // Return as-is if format is unexpected
        }

        $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
        $bankAccountId = $bankAccountParts[1];

        if ($bankAccountType === 'staff_ledger') {
            $staffLedger = StaffLedger::find($bankAccountId);
            return $staffLedger ? $staffLedger->name : null;
        } elseif ($bankAccountType === 'sub_group') {
            $subGroup = AccountSubGroup::find($bankAccountId);
            return $subGroup ? $subGroup->sub_group_name : null;
        }

        return null;
    }

    /**
     * Get bank account details from the format "staff_ledger-{id}" or "sub_group-{id}"
     * 
     * @param string|null $bankField The bank field value in format "staff_ledger-{id}" or "sub_group-{id}"
     * @return array|null Array with 'type', 'id', and 'name' or null if not found
     */
    public static function getBankAccountDetails($bankField)
    {
        if (empty($bankField)) {
            return null;
        }

        // If the bank field doesn't contain a dash, assume it's already a name
        if (strpos($bankField, '-') === false) {
            return [
                'type' => 'name',
                'id' => null,
                'name' => $bankField
            ];
        }

        $bankAccountParts = explode('-', $bankField);
        
        if (count($bankAccountParts) !== 2) {
            return [
                'type' => 'name',
                'id' => null,
                'name' => $bankField
            ];
        }

        $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
        $bankAccountId = $bankAccountParts[1];

        if ($bankAccountType === 'staff_ledger') {
            $staffLedger = StaffLedger::find($bankAccountId);
            return $staffLedger ? [
                'type' => 'staff_ledger',
                'id' => $bankAccountId,
                'name' => $staffLedger->name
            ] : null;
        } elseif ($bankAccountType === 'sub_group') {
            $subGroup = AccountSubGroup::find($bankAccountId);
            return $subGroup ? [
                'type' => 'sub_group',
                'id' => $bankAccountId,
                'name' => $subGroup->sub_group_name
            ] : null;
        }

        return null;
    }
}
