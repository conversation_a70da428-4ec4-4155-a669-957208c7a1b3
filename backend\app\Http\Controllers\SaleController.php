<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\BillNumberGenerator;
use App\Helpers\BankAccountHelper;
use App\Models\Supplier;
use App\Models\PaymentMethod;

class SaleController extends Controller
{
    public function index(Request $request)
    {
        Log::info('Fetching all sales');
        $sales = Sale::with(['items.product.variants'])->get();

        if ($request->has('from') && $request->has('to')) {
            $sales = Sale::with(['items.product.variants'])
                ->whereBetween('created_at', [
                    $request->input('from') . ' 00:00:00',
                    $request->input('to') . ' 23:59:59'
                ])
                ->get();
        }

        // Enhance items with variant data
        $sales->each(function ($sale) {
            $sale->items->each(function ($item) {
                if ($item->product && $item->product->variants->count() > 0) {
                    $variant = $item->product->variants->first();
                    $item->sales_price = $variant->sales_price;
                    $item->variant_mrp = $variant->mrp;
                }
            });
        });

        return response()->json($sales);
    }

    public function show($id)
    {
        Log::info('Fetching sale with ID: ' . $id);
        $sale = Sale::with(['items.product.variants'])->find($id);
        if ($sale) {
            // Enhance items with variant data
            $sale->items->each(function ($item) {
                if ($item->product && $item->product->variants->count() > 0) {
                    $variant = $item->product->variants->first();
                    $item->sales_price = $variant->sales_price;
                    $item->variant_mrp = $variant->mrp;
                }
            });
            return response()->json($sale);
        } else {
            return response()->json(['message' => 'Sale not found'], 404);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'customer_name' => 'required|string',
            'subtotal' => 'required|numeric',
            'discount' => 'required|numeric',
            'tax' => 'nullable|numeric',
            'total' => 'required|numeric',
            'payment_type' => 'required|string',
            'received_amount' => 'required|numeric',
            'balance_amount' => 'required|numeric',
            // Cheque payment validation
            'cheque_no' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'bank' => 'nullable|string|max:255',
            'issue_date' => 'nullable|date',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.mrp' => 'required|numeric|min:0',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount' => 'required|numeric|min:0',
            'items.*.special_discount' => 'nullable|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'items.*.free_qty' => 'nullable|numeric|min:0',
            'items.*.supplier' => 'nullable|string|max:255',
            'items.*.category' => 'nullable|string|max:255',
            'items.*.store_location' => 'nullable|string|max:255',
            // Add variant support
            'items.*.variant_id' => 'nullable|integer',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|string',
        ]);

        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            DB::beginTransaction();
            try {
                $user = $request->user();
                if (!$user) {
                    Log::error('User not authenticated in SaleController@store');
                    return response()->json(['message' => 'Unauthorized: User not authenticated'], 401);
                }
                $userId = $user->id;
                $userName = $user->username ?? $user->name;
                Log::info("Generating bill number for userId: {$userId}, userName: {$userName} (attempt " . ($retryCount + 1) . ")");

                $billNumber = BillNumberGenerator::generateNextBillNumber($userId, $userName);

            $activeSchemes = \App\Models\DiscountScheme::where('active', true)
                ->where(function ($query) {
                    $today = date('Y-m-d');
                    $query->whereNull('start_date')->orWhere('start_date', '<=', $today);
                })
                ->where(function ($query) {
                    $today = date('Y-m-d');
                    $query->whereNull('end_date')->orWhere('end_date', '>=', $today);
                })
                ->get();

            // Determine status based on payment type and amounts
            $status = 'pending'; // default status
            $actualReceived = $request->received_amount + ($request->redeem_amount ?? 0);
            if (strtolower($request->payment_type) === 'credit') {
                if ($actualReceived >= $request->total) {
                    $status = 'paid';
                } elseif ($actualReceived > 0) {
                    $status = 'partial';
                } else {
                    $status = 'pending';
                }
            } else {
                // For non-credit payments (cash, card, etc.), mark as paid if received amount >= total
                $status = ($actualReceived >= $request->total) ? 'paid' : 'partial';
            }

            // Calculate total discount from items
            $totalDiscount = 0;
            
            // Debug: Log the entire request items
            Log::info('Sale items received from frontend:', $request->items);
            
            foreach ($request->items as $item) {
                // Debug: Log each item's free_qty
                Log::info('Processing item:', [
                    'product_name' => $item['product_name'] ?? 'N/A',
                    'free_qty' => $item['free_qty'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 'N/A'
                ]);
                
                $totalDiscount += floatval($item['discount'] ?? 0);
                $totalDiscount += floatval($item['special_discount'] ?? 0);
            }

            $sale = Sale::create([
                'bill_number' => $billNumber,
                'customer_id' => $request->customer_id,
                'customer_name' => $request->customer_name,
                'subtotal' => $request->subtotal,
                'discount' => $totalDiscount, // Use calculated total discount
                'tax' => $request->tax ?? 0,
                'total' => $request->total,
                'payment_type' => $request->payment_type,
                'cheque_no' => $request->cheque_no,
                'bank_name' => $request->bank_name,
                'bank' => BankAccountHelper::getBankAccountName($request->bank),
                'issue_date' => $request->issue_date,
                'received_amount' => $request->received_amount + ($request->redeem_amount ?? 0),
                'balance_amount' => $request->balance_amount,
                'status' => $status,
            ]);

            // Redeem loyalty points if redeem_amount is present
            if ($request->has('redeem_amount') && $request->customer_id) {
                $customer = Customer::find($request->customer_id);
                if ($customer) {
                    $redeemAmount = floatval($request->redeem_amount);
                    $customer->points_redeemed = floatval($customer->points_redeemed) + $redeemAmount;
                    // Do NOT update points_balance directly here
                    $customer->save();

                    // Log the redemption event
                    \App\Models\LoyaltyRedemption::create([
                        'customer_id' => $customer->id,
                        'sale_id' => $sale->id,
                        'redeemed_amount' => $redeemAmount,
                        'user_id' => $userId ?? null,
                        'notes' => 'Redeemed via POS',
                    ]);
                }
            }

            $calculateDiscount = function ($product, $schemes) {
                $basePrice = $product->sales_price ?? 0;
                $categoryName = $product->category_name ?? null;
                $maxDiscountValue = 0;

                foreach ($schemes as $scheme) {
                    if (!$scheme->active) continue;

                    $appliesTo = $scheme->applies_to;
                    $target = $scheme->target;

                    if ($appliesTo === 'product' && $target === $product->product_name) {
                        $discountValue = 0;
                        if ($scheme->type === 'percentage') {
                            $discountValue = ($basePrice * $scheme->value) / 100;
                        } elseif ($scheme->type === 'amount') {
                            $discountValue = $scheme->value;
                        }
                        if ($discountValue > $maxDiscountValue) {
                            $maxDiscountValue = $discountValue;
                        }
                    } elseif ($appliesTo === 'category' && $target === $categoryName) {
                        $discountValue = 0;
                        if ($scheme->type === 'percentage') {
                            $discountValue = ($basePrice * $scheme->value) / 100;
                        } elseif ($scheme->type === 'amount') {
                            $discountValue = $scheme->value;
                        }
                        if ($discountValue > $maxDiscountValue) {
                            $maxDiscountValue = $discountValue;
                        }
                    }
                }
                return min($maxDiscountValue, $basePrice);
            };

            foreach ($request->items as $item) {
                // Debug: Log the item data received from frontend
                \Log::info('Processing sale item:', [
                    'product_name' => $item['product_name'] ?? 'N/A',
                    'free_qty_from_frontend' => $item['free_qty'] ?? 'N/A',
                    'quantity' => $item['quantity'] ?? 'N/A'
                ]);

                // Handle variant products - look up by product_id if available
                $product = null;
                if (isset($item['product_id'])) {
                    $product = Product::find($item['product_id']);
                } else {
                    $product = Product::where('product_name', $item['product_name'])->first();
                }

                // Calculate free_quantity if a 'free' discount scheme applies
                $freeQuantity = 0;
                foreach ($activeSchemes as $scheme) {
                    if ($scheme->type === 'free' && $scheme->buy_quantity && $scheme->free_quantity) {
                        if (
                            ($scheme->applies_to === 'product' && $scheme->target === $item['product_name']) ||
                            ($scheme->applies_to === 'category' && $scheme->target === ($product ? $product->category : null))
                        ) {
                            $freeQuantity = floor($item['quantity'] / $scheme->buy_quantity) * $scheme->free_quantity;
                            \Log::info('Backend calculated free quantity:', [
                                'scheme_name' => $scheme->name,
                                'target' => $scheme->target,
                                'item_product_name' => $item['product_name'],
                                'freeQuantity' => $freeQuantity
                            ]);
                        }
                    }
                }

                if ($product) {
                    $unitPrice = floatval($item['unit_price']);
                    $specialDiscount = floatval($item['special_discount'] ?? 0);
                    $discountAmount = floatval($item['discount'] ?? 0);
                    
                    // Use the frontend-calculated total instead of recalculating
                    $totalPrice = floatval($item['total'] ?? 0);
                    
                    // If frontend total is not provided, fallback to backend calculation
                    if ($totalPrice <= 0) {
                        $totalPrice = ($unitPrice * $item['quantity']) - ($discountAmount + $specialDiscount);
                    }

                    // DO NOT UPDATE STOCK HERE - Stock tracking is handled by the database records
                    // Opening stock should NEVER be modified by sales transactions
                    // Sales are tracked in sale_items table with batch information
                    // Stock calculations are done dynamically in reports

                    // Prepare expiry date - handle invalid formats
                    $expiryDate = null;
                    if (!empty($item['expiry_date']) && $item['expiry_date'] !== 'N/A') {
                        try {
                            $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                            // Validate the date
                            if ($expiryDate === '1970-01-01' || !$expiryDate) {
                                $expiryDate = null;
                            }
                        } catch (\Exception $e) {
                            $expiryDate = null;
                        }
                    }

                    $finalFreeQty = $item['free_qty'] ?? $freeQuantity;
                    \Log::info('Saving sale item with free_qty:', [
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'free_qty_from_frontend' => $item['free_qty'] ?? 'N/A',
                        'freeQuantity_calculated' => $freeQuantity,
                        'final_free_qty' => $finalFreeQty
                    ]);

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'product_id' => $product->product_id,
                        'product_variant_id' => $item['variant_id'] ?? null,
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $expiryDate,
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'quantity' => $item['quantity'],
                        'mrp' => $item['mrp'],
                        'unit_price' => $unitPrice,
                        'discount' => $discountAmount,
                        'special_discount' => $specialDiscount,
                        'total' => max(0, $totalPrice),
                        'supplier' => $item['supplier'] ?? ($product ? $product->supplier : null),
                        'category' => $item['category'] ?? ($product ? $product->category : null),
                        'store_location' => $item['store_location'] ?? ($product ? $product->store_location : null),
                        'free_qty' => $finalFreeQty,
                    ]);
                } else {
                    $unitPrice = floatval($item['unit_price']);
                    $specialDiscount = floatval($item['special_discount'] ?? 0);
                    $discountAmount = floatval($item['discount'] ?? 0);
                    $totalPrice = ($unitPrice * $item['quantity']) - ($discountAmount + $specialDiscount);

                    $finalFreeQty = $item['free_qty'] ?? $freeQuantity;
                    \Log::info('Saving sale item (no product found) with free_qty:', [
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'free_qty_from_frontend' => $item['free_qty'] ?? 'N/A',
                        'freeQuantity_calculated' => $freeQuantity,
                        'final_free_qty' => $finalFreeQty
                    ]);

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'product_id' => null,
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'quantity' => $item['quantity'],
                        'mrp' => $item['mrp'],
                        'unit_price' => $unitPrice,
                        'discount' => $discountAmount,
                        'special_discount' => $specialDiscount,
                        'total' => max(0, $totalPrice),
                        'free_qty' => $finalFreeQty,
                    ]);
                }
            }

            // Save payment details in payment_method table
            PaymentMethod::create([
                'type' => 'Sales',
                'reference_number' => $sale->bill_number,
                'refer_type' => 'Customer',
                'refer_id' => $sale->customer_id,
                'refer_name' => $sale->customer_name,
                'bank' => BankAccountHelper::getBankAccountName($request->bank),
                'total' => $sale->total,
                'payment_type' => $sale->payment_type,
                'cheque_no' => $request->cheque_no,
                'bank_name' => $request->bank_name,
                'issue_date' => $request->issue_date,
                'settled_amount' => $sale->received_amount,
                'balance_amount' => $sale->balance_amount,
                'date' => now()->toDateString(),
            ]);

            // Create bank account ledger entry for online, card, and cheque payments
            if (in_array(strtolower($request->payment_type), ['online', 'card', 'cheque']) &&
                !empty($request->bank) &&
                $request->received_amount > 0) {

                $bankAccountInfo = $request->bank;
                $bankAccountParts = explode('-', $bankAccountInfo);

                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];

                    // Create payment entry for bank account ledger (debit for sales)
                    \App\Models\Payment::create([
                        'voucher_no' => $sale->bill_number . '-BANK',
                        'transaction_id' => $sale->id,
                        'transaction_type' => 'sale_bank',
                        'reference_no' => $sale->bill_number,
                        'refer_type' => $bankAccountType === 'staff_ledger' ? 'Ledger' : 'SubGroup',
                        'refer_id' => $bankAccountId,
                        'refer_name' => $bankAccountType === 'staff_ledger' ?
                            (\App\Models\StaffLedger::find($bankAccountId)->name ?? 'Unknown Bank Account') :
                            (\App\Models\AccountSubGroup::find($bankAccountId)->sub_group_name ?? 'Unknown Bank Account'),
                        'amount' => $request->received_amount, // Positive for debit entry
                        'discount' => 0,
                        'payment_date' => now()->toDateString(),
                        'payment_method' => ucfirst($request->payment_type),
                        'bank' => $bankAccountInfo,
                        'note' => 'Sale payment - Bank account debit entry',
                        'account_type' => 'Bank Accounts',
                    ]);
                }
            }

            // Create cheque statement if payment type is cheque
            if (strtolower($request->payment_type) === 'cheque' && $request->received_amount > 0) {
                // Create a Payment record for cheque statement
                $payment = \App\Models\Payment::create([
                    'voucher_no' => $sale->bill_number,
                    'transaction_id' => $sale->id,
                    'transaction_type' => 'sale',
                    'reference_no' => $sale->bill_number,
                    'refer_type' => 'Customer',
                    'refer_id' => $sale->customer_id,
                    'refer_name' => $sale->customer_name,
                    'amount' => $request->received_amount,
                    'discount' => 0, // Always provide discount
                    'payment_date' => now()->toDateString(),
                    'payment_method' => 'Cheque',
                    'cheque_no' => $request->cheque_no,
                    'bank_name' => $request->bank_name,
                    'issue_date' => $request->issue_date,
                    'note' => 'Sale payment via cheque',
                    'discount' => '0',
                ]);

                // Create cheque statement
                \App\Models\ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending'
                ]);
            }

            DB::commit();
            
            // Log user activity
            \App\Http\Controllers\UserActivityController::logActivity(
                auth()->id(),
                'Sale Created',
                'Sales',
                $sale->id,
                [
                    'bill_number' => $sale->bill_number,
                    'customer_name' => $sale->customer_name,
                    'total' => $sale->total,
                    'payment_type' => $sale->payment_type
                ]
            );
            
            // Temporary debugging response
            return response()->json([
                'message' => 'Sale saved successfully!', 
                'data' => $sale,
                'debug_info' => [
                    'items_processed' => $request->items,
                    'free_qty_debug' => 'Check the items_processed array above to see free_qty values'
                ]
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            
            // Check if it's a duplicate bill number error
            if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'bill_number_unique') !== false) {
                $retryCount++;
                if ($retryCount < $maxRetries) {
                    Log::warning("Duplicate bill number detected, retrying... (attempt " . ($retryCount + 1) . ")");
                    continue; // Retry with a new bill number
                }
            }
            
            return response()->json(['message' => 'Failed to save sale.', 'error' => $e->getMessage()], 500);
        }
        }
    }

    public function update(Request $request, $id)
    {
        Log::info('Update method called for sale ID: ' . $id);
        $sale = Sale::find($id);
        if (!$sale) {
            return response()->json(['message' => 'Sale not found'], 404);
        }

        $request->validate([
            'customer_name' => 'required|string',
            'subtotal' => 'required|numeric',
            'discount' => 'required|numeric',
            'tax' => 'nullable|numeric',
            'total' => 'required|numeric',
            'payment_type' => 'required|string',
            'received_amount' => 'required|numeric',
            'balance_amount' => 'required|numeric',
            // Cheque payment validation
            'cheque_no' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'issue_date' => 'nullable|date',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.mrp' => 'required|numeric|min:0',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount' => 'required|numeric|min:0',
            'items.*.special_discount' => 'nullable|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'items.*.free_qty' => 'nullable|numeric|min:0',
            'items.*.supplier' => 'nullable|string|max:255',
            'items.*.category' => 'nullable|string|max:255',
            'items.*.store_location' => 'nullable|string|max:255',
            // Add variant support
            'items.*.variant_id' => 'nullable|integer',
            'items.*.batch_number' => 'nullable|string|max:255',
            'items.*.expiry_date' => 'nullable|date',
        ]);

        DB::beginTransaction();
        try {
            $activeSchemes = \App\Models\DiscountScheme::where('active', true)
                ->where(function ($query) {
                    $today = date('Y-m-d');
                    $query->whereNull('start_date')->orWhere('start_date', '<=', $today);
                })
                ->where(function ($query) {
                    $today = date('Y-m-d');
                    $query->whereNull('end_date')->orWhere('end_date', '>=', $today);
                })
                ->get();

            // Determine status based on payment type and amounts
            $status = 'pending'; // default status
            $actualReceived = $request->received_amount + ($request->redeem_amount ?? 0);
            if (strtolower($request->payment_type) === 'credit') {
                if ($actualReceived >= $request->total) {
                    $status = 'paid';
                } elseif ($actualReceived > 0) {
                    $status = 'partial';
                } else {
                    $status = 'pending';
                }
            } else {
                // For non-credit payments (cash, card, etc.), mark as paid if received amount >= total
                $status = ($actualReceived >= $request->total) ? 'paid' : 'partial';
            }

            $sale->update([
                'customer_name' => $request->customer_name,
                'subtotal' => $request->subtotal,
                'discount' => $request->discount,
                'tax' => $request->tax ?? 0,
                'total' => $request->total,
                'payment_type' => $request->payment_type,
                'cheque_no' => $request->cheque_no,
                'bank_name' => $request->bank_name,
                'bank' => BankAccountHelper::getBankAccountName($request->bank),
                'issue_date' => $request->issue_date,
                'received_amount' => $request->received_amount,
                'balance_amount' => $request->balance_amount,
                'status' => $status,
            ]);

            $sale->items()->delete();

            $calculateDiscount = function ($product, $schemes) {
                $basePrice = $product->sales_price ?? 0;
                $categoryName = $product->category_name ?? null;
                $maxDiscountValue = 0;

                foreach ($schemes as $scheme) {
                    if (!$scheme->active) continue;

                    $appliesTo = $scheme->applies_to;
                    $target = $scheme->target;

                    if ($appliesTo === 'product' && $target === $product->product_name) {
                        $discountValue = 0;
                        if ($scheme->type === 'percentage') {
                            $discountValue = ($basePrice * $scheme->value) / 100;
                        } elseif ($scheme->type === 'amount') {
                            $discountValue = $scheme->value;
                        }
                        if ($discountValue > $maxDiscountValue) {
                            $maxDiscountValue = $discountValue;
                        }
                    } elseif ($appliesTo === 'category' && $target === $categoryName) {
                        $discountValue = 0;
                        if ($scheme->type === 'percentage') {
                            $discountValue = ($basePrice * $scheme->value) / 100;
                        } elseif ($scheme->type === 'amount') {
                            $discountValue = $scheme->value;
                        }
                        if ($discountValue > $maxDiscountValue) {
                            $maxDiscountValue = $discountValue;
                        }
                    }
                }
                return min($maxDiscountValue, $basePrice);
            };

            foreach ($request->items as $item) {
                // Handle variant products - look up by product_id if available
                $product = null;
                if (isset($item['product_id'])) {
                    $product = Product::find($item['product_id']);
                } else {
                    $product = Product::where('product_name', $item['product_name'])->first();
                }

                // Calculate free_quantity if a 'free' discount scheme applies
                $freeQuantity = 0;
                foreach ($activeSchemes as $scheme) {
                    if ($scheme->type === 'free' && $scheme->buy_quantity && $scheme->free_quantity) {
                        if (
                            ($scheme->applies_to === 'product' && $scheme->target === $item['product_name']) ||
                            ($scheme->applies_to === 'category' && $scheme->target === ($product ? $product->category : null))
                        ) {
                            $freeQuantity = floor($item['quantity'] / $scheme->buy_quantity) * $scheme->free_quantity;
                        }
                    }
                }

                if ($product) {
                    $unitPrice = floatval($item['unit_price']);
                    $specialDiscount = floatval($item['special_discount'] ?? 0);
                    $discountAmount = floatval($item['discount'] ?? 0);
                    $totalPrice = ($unitPrice * $item['quantity']) - ($discountAmount + $specialDiscount);

                    if ($item['quantity'] <= 0) {
                        Log::warning('Invalid quantity for product ' . ($item['display_name'] ?? $item['product_name']) . ': ' . $item['quantity']);
                        continue;
                    }

                    // Prepare expiry date - handle invalid formats
                    $expiryDate = null;
                    if (!empty($item['expiry_date']) && $item['expiry_date'] !== 'N/A') {
                        try {
                            $expiryDate = date('Y-m-d', strtotime($item['expiry_date']));
                            // Validate the date
                            if ($expiryDate === '1970-01-01' || !$expiryDate) {
                                $expiryDate = null;
                            }
                        } catch (\Exception $e) {
                            $expiryDate = null;
                        }
                    }

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'product_id' => $product->product_id,
                        'product_variant_id' => $item['variant_id'] ?? null,
                        'batch_number' => $item['batch_number'] ?? null,
                        'expiry_date' => $expiryDate,
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'quantity' => $item['quantity'],
                        'mrp' => $item['mrp'],
                        'unit_price' => $unitPrice,
                        'discount' => $discountAmount,
                        'special_discount' => $specialDiscount,
                        'total' => max(0, $totalPrice),
                        'supplier' => $item['supplier'] ?? ($product ? $product->supplier : null),
                        'category' => $item['category'] ?? ($product ? $product->category : null),
                        'store_location' => $item['store_location'] ?? ($product ? $product->store_location : null),
                        'free_qty' => $item['free_qty'] ?? $freeQuantity,
                    ]);
                } else {
                    $unitPrice = floatval($item['unit_price']);
                    $specialDiscount = floatval($item['special_discount'] ?? 0);
                    $discountAmount = floatval($item['discount'] ?? 0);
                    $totalPrice = ($unitPrice * $item['quantity']) - ($discountAmount + $specialDiscount);

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'product_id' => null,
                        'product_name' => $item['display_name'] ?? $item['product_name'],
                        'quantity' => $item['quantity'],
                        'mrp' => $item['mrp'],
                        'unit_price' => $unitPrice,
                        'discount' => $discountAmount,
                        'special_discount' => $specialDiscount,
                        'total' => max(0, $totalPrice),
                        'free_qty' => $item['free_qty'] ?? $freeQuantity,
                    ]);
                }
            }

            // Update payment details in payment_method table
            PaymentMethod::updateOrCreate(
                [
                    'type' => 'Sales',
                    'reference_number' => $sale->bill_number,
                ],
                [
                    'refer_type' => 'Customer',
                    'refer_id' => $sale->customer_id,
                    'refer_name' => $sale->customer_name,
                    'bank' => BankAccountHelper::getBankAccountName($request->bank),
                    'total' => $sale->total,
                    'payment_type' => $sale->payment_type,
                    'cheque_no' => $request->cheque_no,
                    'bank_name' => $request->bank_name,
                    'issue_date' => $request->issue_date,
                    'settled_amount' => $sale->received_amount,
                    'balance_amount' => $sale->balance_amount,
                    'date' => now()->toDateString(),
                ]
            );

            // Handle cheque statement for updates
            if (strtolower($request->payment_type) === 'cheque' && $request->received_amount > 0) {
                // Delete existing cheque statement if payment type changed from cheque
                \App\Models\ChequeStatement::where('voucher_no', $sale->bill_number)->delete();
                
                // Create a Payment record for cheque statement
                $payment = \App\Models\Payment::create([
                    'voucher_no' => $sale->bill_number,
                    'transaction_id' => $sale->id,
                    'transaction_type' => 'sale',
                    'reference_no' => $sale->bill_number,
                    'refer_type' => 'Customer',
                    'refer_id' => $sale->customer_id,
                    'refer_name' => $sale->customer_name,
                    'amount' => $request->received_amount,
                    'discount' => 0, // Always provide discount
                    'payment_date' => now()->toDateString(),
                    'payment_method' => 'Cheque',
                    'cheque_no' => $request->cheque_no,
                    'bank_name' => $request->bank_name,
                    'issue_date' => $request->issue_date,
                    'note' => 'Sale payment via cheque',
                ]);

                // Create cheque statement
                \App\Models\ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending'
                ]);
            } else {
                // If payment type is not cheque, delete any existing cheque statement
                \App\Models\ChequeStatement::where('voucher_no', $sale->bill_number)->delete();
            }

            DB::commit();
            return response()->json(['message' => 'Sale updated successfully!', 'data' => $sale], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update sale: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to update sale.', 'error' => $e->getMessage()], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        Log::info('Deleting sale with ID: ' . $id);
        $sale = Sale::find($id);
        if (!$sale) {
            return response()->json(['message' => 'Sale not found'], 404);
        }

        DB::beginTransaction();
        try {
            // Delete associated payment method record
            PaymentMethod::where('type', 'Sales')
                ->where('reference_number', $sale->bill_number)
                ->delete();

            // Accept deleted_by from request, fallback to authenticated user
            $deletedBy = $request->input('deleted_by') ?? $request->user()->id ?? auth()->id();
            if (!$deletedBy) {
                Log::warning('No user ID found when deleting sale', [
                    'request_user' => $request->user(),
                    'auth_id' => auth()->id(),
                ]);
                return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
            }

            // Soft delete sale items (they will be restored with the sale)
            foreach ($sale->items as $item) {
                $item->deleted_by = $deletedBy;
                $item->save();
                $item->delete();
            }
            // Soft delete the sale
            $sale->deleted_by = $deletedBy;
            $sale->save();
            $sale->delete();

            // Log the delete activity
            \App\Http\Controllers\UserActivityController::logActivity(
                $deletedBy,
                'Sale Deleted',
                'Sales',
                $sale->id,
                [
                    'bill_number' => $sale->bill_number,
                    'customer_name' => $sale->customer_name,
                    'total_amount' => $sale->total,
                    'deleted_by' => $deletedBy
                ]
            );

            DB::commit();
            return response()->json(['message' => 'Sale and all items moved to trash'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting sale: ' . $e->getMessage());
            return response()->json(['message' => 'Error deleting sale', 'error' => $e->getMessage()], 500);
        }
    }

    public function getLastBillNumber(Request $request)
    {
        $user = $request->user();
        $userId = $user ? $user->id : ($request->input('user_id', 'U0'));
        $userName = $user ? ($user->username ?: $user->name) : ($request->input('user_name', 'NON'));

        $nextBillNumber = BillNumberGenerator::generateNextBillNumber($userId, $userName);
        return response()->json(['next_bill_number' => $nextBillNumber]);
    }

    public function getCombinedBillWiseProfitReport(Request $request)
    {
        try {
            $salesQuery = Sale::with(['items.product'])
                ->select('id', 'bill_number', 'created_at', 'customer_name', 'payment_type');

            $invoicesQuery = \App\Models\Invoice::with('items')
                ->select('id', 'invoice_no', 'invoice_date', 'customer_name', 'payment_method');

            if ($request->has('fromDate') && $request->has('toDate')) {
                $salesQuery->whereBetween('created_at', [
                    $request->input('fromDate') . ' 00:00:00',
                    $request->input('toDate') . ' 23:59:59'
                ]);
                $invoicesQuery->whereBetween('invoice_date', [
                    $request->input('fromDate') . ' 00:00:00',
                    $request->input('toDate') . ' 23:59:59'
                ]);
            }

            if ($request->has('paymentMethod')) {
                $paymentMethod = trim(strtolower($request->input('paymentMethod')));
                if ($paymentMethod !== '' && $paymentMethod !== 'all') {
                    $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                    $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
                }
            }

            $sales = $salesQuery->get();
            $invoices = $invoicesQuery->get();

            $combinedReportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;

            foreach ($sales as $sale) {
                $totalCostPrice = 0;
                $totalSellingPrice = 0;
                $items = [];

                foreach ($sale->items as $item) {
                    $product = $item->product;
                    $buyingCost = $product ? $product->buying_cost : 0;

                    $itemCostPrice = $buyingCost * $item->quantity;
                    $itemSellingPrice = $item->unit_price * $item->quantity;
                    $itemProfit = $itemSellingPrice - $itemCostPrice;
                    $itemProfitPercentage = ($itemSellingPrice > 0) ? ($itemProfit / $itemSellingPrice) * 100 : 0;

                    $items[] = [
                        'product_name' => $item->product_name,
                        'quantity' => $item->quantity,
                        'costPrice' => number_format($itemCostPrice, 2),
                        'sellingPrice' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                        'profitPercentage' => number_format($itemProfitPercentage, 2) . '%',
                    ];

                    $totalCostPrice += $itemCostPrice;
                    $totalSellingPrice += $itemSellingPrice;
                }

                $totalProfit = $totalSellingPrice - $totalCostPrice;
                $profitPercentage = ($totalSellingPrice > 0) ? ($totalProfit / $totalSellingPrice) * 100 : 0;

                $combinedReportData[] = [
                    'bill_number' => $sale->bill_number,
                    'date' => $sale->created_at->format('d-m-Y'),
                    'customer_name' => $sale->customer_name ?: 'Walk-in Customer',
                    'payment_type' => $sale->payment_type,
                    'items' => $items,
                    'totalCostPrice' => number_format($totalCostPrice, 2),
                    'totalSellingPrice' => number_format($totalSellingPrice, 2),
                    'totalProfit' => number_format($totalProfit, 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                ];

                $totalCostPriceAll += $totalCostPrice;
                $totalSellingPriceAll += $totalSellingPrice;
                $totalProfitAll += $totalProfit;
            }

            foreach ($invoices as $invoice) {
                $totalCostPrice = 0;
                $totalSellingPrice = 0;
                $items = [];

                foreach ($invoice->items as $item) {
                    $costPrice = $item->total_buying_cost ?? 0;
                    $sellingPrice = $item->sales_price * $item->quantity;
                    $profit = $sellingPrice - $costPrice;
                    $profitPercentage = ($sellingPrice > 0) ? ($profit / $sellingPrice) * 100 : 0;

                    $items[] = [
                        'product_name' => $item->description,
                        'quantity' => $item->quantity,
                        'costPrice' => number_format($costPrice, 2),
                        'sellingPrice' => number_format($sellingPrice, 2),
                        'profit' => number_format($profit, 2),
                        'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    ];

                    $totalCostPrice += $costPrice;
                    $totalSellingPrice += $sellingPrice;
                }

                $totalProfit = $totalSellingPrice - $totalCostPrice;
                $profitPercentage = ($totalSellingPrice > 0) ? ($totalProfit / $totalSellingPrice) * 100 : 0;

                $combinedReportData[] = [
                    'bill_number' => $invoice->invoice_no,
                    'date' => $invoice->invoice_date->format('d-m-Y'),
                    'customer_name' => $invoice->customer_name ?: 'Walk-in Customer',
                    'payment_type' => $invoice->payment_method,
                    'items' => $items,
                    'totalCostPrice' => number_format($totalCostPrice, 2),
                    'totalSellingPrice' => number_format($totalSellingPrice, 2),
                    'totalProfit' => number_format($totalProfit, 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                ];

                $totalCostPriceAll += $totalCostPrice;
                $totalSellingPriceAll += $totalSellingPrice;
                $totalProfitAll += $totalProfit;
            }

            usort($combinedReportData, function ($a, $b) {
                $dateA = \DateTime::createFromFormat('d-m-Y', $a['date']);
                $dateB = \DateTime::createFromFormat('d-m-Y', $b['date']);
                return $dateB <=> $dateA;
            });

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $combinedReportData,
                'summary' => $summary,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getCombinedBillWiseProfitReport: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch combined report.'], 500);
        }
    }

    public function getDailyProfitReport(Request $request)
    {
        try {
            $date = $request->input('date', now()->format('Y-m-d'));
            $sales = Sale::with('items.product')
                ->whereDate('created_at', $date)
                ->get();

            $reportData = [];
            $totalCost = 0;
            $totalSales = 0;
            $totalProfit = 0;

            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    $product = $item->product;
                    $productName = $product ? $product->product_name : 'Unknown Product';
                    $costPrice = $product ? $product->buying_cost * $item->quantity : 0;
                    $sellingPrice = $item->unit_price * $item->quantity;
                    $profit = $sellingPrice - $costPrice;

                    if (!isset($reportData[$productName])) {
                        $reportData[$productName] = [
                            'product_name' => $productName,
                            'total_quantity_sold' => 0,
                            'total_sales_amount' => 0,
                            'total_cost' => 0,
                            'total_profit' => 0,
                        ];
                    }

                    $reportData[$productName]['total_quantity_sold'] += $item->quantity;
                    $reportData[$productName]['total_sales_amount'] += $sellingPrice;
                    $reportData[$productName]['total_cost'] += $costPrice;
                    $reportData[$productName]['total_profit'] += $profit;

                    $totalCost += $costPrice;
                    $totalSales += $sellingPrice;
                    $totalProfit += $profit;
                }
            }

            $reportData = array_map(function ($item) {
                $profitPercentage = ($item['total_sales_amount'] > 0) ? ($item['total_profit'] / $item['total_sales_amount']) * 100 : 0;
                return [
                    'product_name' => $item['product_name'],
                    'total_quantity_sold' => number_format($item['total_quantity_sold'], 2),
                    'total_sales_amount' => number_format($item['total_sales_amount'], 2),
                    'total_cost' => number_format($item['total_cost'], 2),
                    'total_profit' => number_format($item['total_profit'], 2),
                    'profit_percentage' => number_format($profitPercentage, 2) . '%',
                ];
            }, array_values($reportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCost, 2),
                'totalSellingPriceAll' => number_format($totalSales, 2),
                'totalProfitAll' => number_format($totalProfit, 2),
                'averageProfitPercentageAll' => ($totalSales > 0) ? number_format(($totalProfit / $totalSales) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getDailyProfitReport: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch report.'], 500);
        }
    }

    public function getCombinedDailyProfitReport(Request $request)
    {
        try {
            $date = $request->input('date', now()->format('Y-m-d'));

            $sales = Sale::with(['items.product'])
                ->whereDate('created_at', $date)
                ->get();
            $invoices = \App\Models\Invoice::with('items.product')
                ->whereDate('invoice_date', $date)
                ->get();

            $combinedReportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;

            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    $product = $item->product;
                    $productName = $product ? $product->product_name : 'Unknown Product';
                    $costPrice = $product ? $product->buying_cost * $item->quantity : 0;
                    $sellingPrice = $item->unit_price * $item->quantity;
                    $profit = $sellingPrice - $costPrice;

                    if (!isset($combinedReportData[$productName])) {
                        $combinedReportData[$productName] = [
                            'product_name' => $productName,
                            'total_quantity_sold' => 0,
                            'total_sales_amount' => 0,
                            'total_cost' => 0,
                            'total_profit' => 0,
                        ];
                    }

                    $combinedReportData[$productName]['total_quantity_sold'] += $item->quantity;
                    $combinedReportData[$productName]['total_sales_amount'] += $sellingPrice;
                    $combinedReportData[$productName]['total_cost'] += $costPrice;
                    $combinedReportData[$productName]['total_profit'] += $profit;
                    $totalCostPriceAll += $costPrice;
                    $totalSellingPriceAll += $sellingPrice;
                    $totalProfitAll += $profit;
                }
            }

            foreach ($invoices as $invoice) {
                foreach ($invoice->items as $item) {
                    $product = $item->product;
                    $productName = $product ? $product->product_name : ($item->description ?? 'Unknown Product');
                    $costPrice = $product ? $product->buying_cost * $item->quantity : 0;
                    $sellingPrice = $item->sales_price * $item->quantity;
                    $profit = $sellingPrice - $costPrice;

                    if (!isset($combinedReportData[$productName])) {
                        $combinedReportData[$productName] = [
                            'product_name' => $productName,
                            'total_quantity_sold' => 0,
                            'total_sales_amount' => 0,
                            'total_cost' => 0,
                            'total_profit' => 0,
                        ];
                    }

                    $combinedReportData[$productName]['total_quantity_sold'] += $item->quantity;
                    $combinedReportData[$productName]['total_sales_amount'] += $sellingPrice;
                    $combinedReportData[$productName]['total_cost'] += $costPrice;
                    $combinedReportData[$productName]['total_profit'] += $profit;

                    $totalCostPriceAll += $costPrice;
                    $totalSellingPriceAll += $sellingPrice;
                    $totalProfitAll += $profit;
                }
            }

            $combinedReportData = array_map(function ($item) {
                $profitPercentage = ($item['total_sales_amount'] > 0) ? ($item['total_profit'] / $item['total_sales_amount']) * 100 : 0;
                return [
                    'product_name' => $item['product_name'],
                    'total_quantity_sold' => number_format($item['total_quantity_sold'], 2),
                    'total_sales_amount' => number_format($item['total_sales_amount'], 2),
                    'total_cost' => number_format($item['total_cost'], 2),
                    'total_profit' => number_format($item['total_profit'], 2),
                    'profit_percentage' => number_format($profitPercentage, 2) . '%',
                ];
            }, array_values($combinedReportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $combinedReportData,
                'summary' => $summary,
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getCombinedDailyProfitReport: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch combined daily profit report.'], 500);
        }
    }

public function getCompanyWiseProfitReport(Request $request)
{
    try {
        $companyName = trim($request->input('companyName'));
        $salesQuery = Sale::with(['items.product'])
            ->select('id', 'created_at', 'customer_name', 'payment_type');

        $invoicesQuery = \App\Models\Invoice::with('items')
            ->select('id', 'invoice_date', 'customer_name', 'payment_method');

        if ($request->has('fromDate') && $request->has('toDate')) {
            $salesQuery->whereBetween('created_at', [
                $request->input('fromDate') . ' 00:00:00',
                $request->input('toDate') . ' 23:59:59'
            ]);
            $invoicesQuery->whereBetween('invoice_date', [
                $request->input('fromDate') . ' 00:00:00',
                $request->input('toDate') . ' 23:59:59'
            ]);
        }

        if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '') {
            $paymentMethod = trim(strtolower($request->input('paymentMethod')));
            if ($paymentMethod !== '' && $paymentMethod !== 'all') {
                $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
            }
        }

if (!empty($companyName)) {
    $salesQuery->whereHas('items.product', function ($q) use ($companyName) {
        $q->whereRaw('LOWER(company) = ?', [strtolower($companyName)]);
    });
    $invoicesQuery->whereHas('items.product', function ($q) use ($companyName) {
        $q->whereRaw('LOWER(company) = ?', [strtolower($companyName)]);
    });
}

        $sales = $salesQuery->get();
        $invoices = $invoicesQuery->get();

        $reportData = [];
        $totalCostPriceAll = 0;
        $totalSellingPriceAll = 0;
        $totalProfitAll = 0;
        $totalQuantityAll = 0;

        foreach ($sales as $sale) {
            foreach ($sale->items as $item) {
                $product = $item->product;
                $companyName = $product ? $product->company : 'Unknown Company';

                $buyingCost = $product ? floatval($product->buying_cost ?? 0) : 0;
                $quantity = floatval($item->quantity ?? 0);
                $unitPrice = floatval($item->unit_price ?? 0);
                $discount = floatval($item->discount ?? 0);
                $specialDiscount = floatval($item->special_discount ?? 0);

                $itemCostPrice = $buyingCost * $quantity;
                $itemSellingPrice = ($unitPrice * $quantity) - ($discount + $specialDiscount);
                $itemProfit = $itemSellingPrice - $itemCostPrice;

                if (!isset($reportData[$companyName])) {
                    $reportData[$companyName] = [
                        'companyName' => $companyName,
                        'totalCostPrice' => 0,
                        'totalSellingPrice' => 0,
                        'totalProfit' => 0,
                        'totalQuantity' => 0,
                        'items' => [],
                    ];
                }

                $reportData[$companyName]['totalCostPrice'] += $itemCostPrice;
                $reportData[$companyName]['totalSellingPrice'] += $itemSellingPrice;
                $reportData[$companyName]['totalProfit'] += $itemProfit;
                $reportData[$companyName]['totalQuantity'] += $quantity;

                $reportData[$companyName]['items'][] = [
                    'product_name' => $item->product_name ?? 'Unknown Product',
                    'quantity' => number_format($quantity, 2),
                    'unit_price' => number_format($unitPrice, 2),
                    'total_cost' => number_format($itemCostPrice, 2),
                    'total_sales' => number_format($itemSellingPrice, 2),
                    'profit' => number_format($itemProfit, 2),
                ];

                $totalCostPriceAll += $itemCostPrice;
                $totalSellingPriceAll += $itemSellingPrice;
                $totalProfitAll += $itemProfit;
                $totalQuantityAll += $quantity;
            }
        }

        foreach ($invoices as $invoice) {
            foreach ($invoice->items as $item) {
                $product = $item->product;
                $companyName = $product ? $product->company : 'Unknown Company';

                $costPrice = $product ? floatval($product->buying_cost ?? 0) * $item->quantity : floatval($item->total_buying_cost ?? 0);
                $quantity = floatval($item->quantity ?? 0);
                $unitPrice = floatval($item->sales_price ?? $item->unit_price ?? 0);
                $discount = floatval($item->discount_amount ?? 0);
                $specialDiscount = floatval($item->special_discount ?? 0);

                $itemCostPrice = $costPrice;
                $itemSellingPrice = ($quantity * $unitPrice) - $specialDiscount ;
                $itemProfit = $itemSellingPrice - $itemCostPrice;

                if (!isset($reportData[$companyName])) {
                    $reportData[$companyName] = [
                        'companyName' => $companyName,
                        'totalCostPrice' => 0,
                        'totalSellingPrice' => 0,
                        'totalProfit' => 0,
                        'totalQuantity' => 0,
                        'items' => [],
                    ];
                }

                $reportData[$companyName]['totalCostPrice'] += $itemCostPrice;
                $reportData[$companyName]['totalSellingPrice'] += $itemSellingPrice;
                $reportData[$companyName]['totalProfit'] += $itemProfit;
                $reportData[$companyName]['totalQuantity'] += $quantity;

                $reportData[$companyName]['items'][] = [
                    'product_name' => $item->description ?? 'Unknown Product',
                    'quantity' => number_format($quantity, 2),
                    'unit_price' => number_format($unitPrice, 2),
                    'total_cost' => number_format($itemCostPrice, 2),
                    'total_sales' => number_format($itemSellingPrice, 2),
                    'profit' => number_format($itemProfit, 2),
                ];

                $totalCostPriceAll += $itemCostPrice;
                $totalSellingPriceAll += $itemSellingPrice;
                $totalProfitAll += $itemProfit;
                $totalQuantityAll += $quantity;
            }
        }

        $reportData = array_map(function ($item) {
            $profitPercentage = ($item['totalSellingPrice'] > 0) ? ($item['totalProfit'] / $item['totalSellingPrice']) * 100 : 0;
            return [
                'companyName' => $item['companyName'],
                'totalCostPrice' => number_format($item['totalCostPrice'], 2),
                'totalSellingPrice' => number_format($item['totalSellingPrice'], 2),
                'totalProfit' => number_format($item['totalProfit'], 2),
                'totalQuantity' => number_format($item['totalQuantity'], 2),
                'profitPercentage' => number_format($profitPercentage, 2) . '%',
                'items' => $item['items'],
            ];
        }, array_values($reportData));

        $summary = [
            'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
            'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
            'totalProfitAll' => number_format($totalProfitAll, 2),
            'totalQuantityAll' => number_format($totalQuantityAll, 2),
            'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
        ];

        return response()->json([
            'reportData' => $reportData,
            'summary' => $summary,
        ]);
    } catch (\Exception $e) {
        Log::error('Error in getCompanyWiseProfitReport: ' . $e->getMessage(), [
            'exception' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'stack_trace' => $e->getTraceAsString(),
            'request' => $request->all(),
        ]);
        return response()->json(['error' => 'Failed to fetch report.'], 500);
    }
}

    public function getSupplierWiseProfitReport(Request $request)
    {
        try {
            $supplierName = trim($request->input('supplierName'));
            if (empty($supplierName)) {
                Log::warning('No supplier name provided in getSupplierWiseProfitReport');
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            // Sanitize supplier name for query (case-insensitive)
            $supplier = Supplier::whereRaw('LOWER(supplier_name) = ?', [strtolower($supplierName)])->first();
            if (!$supplier) {
                Log::info("Supplier not found: {$supplierName}");
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            $salesQuery = Sale::with(['items.product'])
                ->select('id', 'created_at', 'customer_name', 'payment_type')
                ->whereHas('items', function ($q) use ($supplierName) {
                    $q->whereRaw('LOWER(supplier) = ?', [strtolower($supplierName)]);
                });

            $invoicesQuery = Invoice::with('items')
                ->select('id', 'invoice_date', 'customer_name', 'payment_method')
                ->whereHas('items', function ($q) use ($supplierName) {
                    $q->whereRaw('LOWER(supplier) = ?', [strtolower($supplierName)]);
                });

            if ($request->has('fromDate') && $request->has('toDate')) {
                $fromDate = $request->input('fromDate');
                $toDate = $request->input('toDate');
                if ($fromDate && $toDate) {
                    $salesQuery->whereBetween('created_at', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                    $invoicesQuery->whereBetween('invoice_date', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                }
            }

            if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '' && $request->input('paymentMethod') !== 'all') {
                $paymentMethod = trim(strtolower($request->input('paymentMethod')));
                $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
            }

            $sales = $salesQuery->get();
            $invoices = $invoicesQuery->get();

            $reportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;
            $totalQuantityAll = 0;

            // Process sales
            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    $currentSupplierName = $item->supplier ? trim($item->supplier) : 'Unknown Supplier';
                    if (strtolower($currentSupplierName) !== strtolower($supplierName)) {
                        continue;
                    }

                    $product = $item->product;
                    $buyingCost = $product ? floatval($product->buying_cost ?? 0) : 0;
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->unit_price ?? 0);
                    $discount = floatval($item->discount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $buyingCost * $quantity;
                    $itemSellingPrice = ($unitPrice * $quantity) - ($discount + $specialDiscount);
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentSupplierName])) {
                        $reportData[$currentSupplierName] = [
                            'supplierName' => $currentSupplierName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentSupplierName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentSupplierName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentSupplierName]['totalProfit'] += $itemProfit;
                    $reportData[$currentSupplierName]['totalQuantity'] += $quantity;

                    $reportData[$currentSupplierName]['items'][] = [
                        'product_name' => $item->product_name ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            // Process invoices
            foreach ($invoices as $invoice) {
                foreach ($invoice->items as $item) {
                    $currentSupplierName = $item->supplier ? trim($item->supplier) : 'Unknown Supplier';
                    if (strtolower($currentSupplierName) !== strtolower($supplierName)) {
                        continue;
                    }

                    $costPrice = floatval($item->total_buying_cost ?? 0);
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->sales_price ?? $item->unit_price ?? 0);
                    $discount = floatval($item->discount_amount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $costPrice;
                    $itemSellingPrice = ($quantity * $unitPrice) - $specialDiscount ;
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentSupplierName])) {
                        $reportData[$currentSupplierName] = [
                            'supplierName' => $currentSupplierName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentSupplierName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentSupplierName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentSupplierName]['totalProfit'] += $itemProfit;
                    $reportData[$currentSupplierName]['totalQuantity'] += $quantity;

                    $reportData[$currentSupplierName]['items'][] = [
                        'product_name' => $item->description ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            $reportData = array_map(function ($item) {
                $profitPercentage = ($item['totalSellingPrice'] > 0) ? ($item['totalProfit'] / $item['totalSellingPrice']) * 100 : 0;
                return [
                    'supplierName' => $item['supplierName'],
                    'totalCostPrice' => number_format($item['totalCostPrice'], 2),
                    'totalSellingPrice' => number_format($item['totalSellingPrice'], 2),
                    'totalProfit' => number_format($item['totalProfit'], 2),
                    'totalQuantity' => number_format($item['totalQuantity'], 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    'items' => $item['items'],
                ];
            }, array_values($reportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'totalQuantityAll' => number_format($totalQuantityAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in getSupplierWiseProfitReport: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Failed to fetch supplier report. Please try again later.',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function getCategoryWiseProfitReport(Request $request)
    {
        try {
            $categoryName = trim($request->input('categoryName'));
            if (empty($categoryName)) {
                Log::warning('No category name provided in getCategoryWiseProfitReport');
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            $salesQuery = Sale::with(['items.product'])
                ->select('id', 'created_at', 'customer_name', 'payment_type')
                ->whereHas('items', function ($q) use ($categoryName) {
                    $q->whereRaw('LOWER(category) = ?', [strtolower($categoryName)]);
                });

            $invoicesQuery = Invoice::with('items')
                ->select('id', 'invoice_date', 'customer_name', 'payment_method')
                ->whereHas('items', function ($q) use ($categoryName) {
                    $q->whereRaw('LOWER(category) = ?', [strtolower($categoryName)]);
                });

            if ($request->has('fromDate') && $request->has('toDate')) {
                $fromDate = $request->input('fromDate');
                $toDate = $request->input('toDate');
                if ($fromDate && $toDate) {
                    $salesQuery->whereBetween('created_at', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                    $invoicesQuery->whereBetween('invoice_date', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                }
            }

            if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '' && $request->input('paymentMethod') !== 'all') {
                $paymentMethod = trim(strtolower($request->input('paymentMethod')));
                $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
            }

            $sales = $salesQuery->get();
            $invoices = $invoicesQuery->get();

            $reportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;
            $totalQuantityAll = 0;

            // Process sales
            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    $currentCategoryName = $item->category ? trim($item->category) : 'Unknown Category';
                    if (strtolower($currentCategoryName) !== strtolower($categoryName)) {
                        continue;
                    }

                    $product = $item->product;
                    $buyingCost = $product ? floatval($product->buying_cost ?? 0) : 0;
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->unit_price ?? 0);
                    $discount = floatval($item->discount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $buyingCost * $quantity;
                    $itemSellingPrice = ($unitPrice * $quantity) - ($discount + $specialDiscount);
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentCategoryName])) {
                        $reportData[$currentCategoryName] = [
                            'categoryName' => $currentCategoryName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentCategoryName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentCategoryName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentCategoryName]['totalProfit'] += $itemProfit;
                    $reportData[$currentCategoryName]['totalQuantity'] += $quantity;

                    $reportData[$currentCategoryName]['items'][] = [
                        'product_name' => $item->product_name ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            // Process invoices
            foreach ($invoices as $invoice) {
                foreach ($invoice->items as $item) {
                    $currentCategoryName = $item->category ? trim($item->category) : 'Unknown Category';
                    if (strtolower($currentCategoryName) !== strtolower($categoryName)) {
                        continue;
                    }

                    $costPrice = floatval($item->total_buying_cost ?? 0);
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->sales_price ?? $item->unit_price ?? 0);
                    $discount = floatval($item->discount_amount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $costPrice;
                    $itemSellingPrice = ($unitPrice * $quantity) - $specialDiscount;
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentCategoryName])) {
                        $reportData[$currentCategoryName] = [
                            'categoryName' => $currentCategoryName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentCategoryName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentCategoryName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentCategoryName]['totalProfit'] += $itemProfit;
                    $reportData[$currentCategoryName]['totalQuantity'] += $quantity;

                    $reportData[$currentCategoryName]['items'][] = [
                        'product_name' => $item->description ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            $reportData = array_map(function ($item) {
                $profitPercentage = ($item['totalSellingPrice'] > 0) ? ($item['totalProfit'] / $item['totalSellingPrice']) * 100 : 0;
                return [
                    'categoryName' => $item['categoryName'],
                    'totalCostPrice' => number_format($item['totalCostPrice'], 2),
                    'totalSellingPrice' => number_format($item['totalSellingPrice'], 2),
                    'totalProfit' => number_format($item['totalProfit'], 2),
                    'totalQuantity' => number_format($item['totalQuantity'], 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    'items' => $item['items'],
                ];
            }, array_values($reportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'totalQuantityAll' => number_format($totalQuantityAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in getCategoryWiseProfitReport: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Failed to fetch category report. Please try again later.',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function getStoreLocationWiseProfitReport(Request $request)
    {
        try {
            $storeLocation = trim($request->input('storeLocation'));
            if (empty($storeLocation)) {
                Log::warning('No store location provided in getStoreLocationWiseProfitReport');
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            $salesQuery = Sale::with(['items.product'])
                ->select('id', 'created_at', 'customer_name', 'payment_type')
                ->whereHas('items', function ($q) use ($storeLocation) {
                    $q->whereRaw('LOWER(store_location) = ?', [strtolower($storeLocation)]);
                });

            $invoicesQuery = Invoice::with('items')
                ->select('id', 'invoice_date', 'customer_name', 'payment_method')
                ->whereHas('items', function ($q) use ($storeLocation) {
                    $q->whereRaw('LOWER(store_location) = ?', [strtolower($storeLocation)]);
                });

            if ($request->has('fromDate') && $request->has('toDate')) {
                $fromDate = $request->input('fromDate');
                $toDate = $request->input('toDate');
                if ($fromDate && $toDate) {
                    $salesQuery->whereBetween('created_at', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                    $invoicesQuery->whereBetween('invoice_date', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                }
            }

            if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '' && $request->input('paymentMethod') !== 'all') {
                $paymentMethod = trim(strtolower($request->input('paymentMethod')));
                $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
            }

            $sales = $salesQuery->get();
            $invoices = $invoicesQuery->get();

            $reportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;
            $totalQuantityAll = 0;

            foreach ($sales as $sale) {
                foreach ($sale->items as $item) {
                    $currentStoreLocation = $item->store_location ? trim($item->store_location) : 'Unknown Location';
                    if (strtolower($currentStoreLocation) !== strtolower($storeLocation)) {
                        continue;
                    }

                    $product = $item->product;
                    $buyingCost = $product ? floatval($product->buying_cost ?? 0) : 0;
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->unit_price ?? 0);
                    $discount = floatval($item->discount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $buyingCost * $quantity;
                    $itemSellingPrice = ($unitPrice * $quantity) - ($discount + $specialDiscount);
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentStoreLocation])) {
                        $reportData[$currentStoreLocation] = [
                            'storeLocation' => $currentStoreLocation,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentStoreLocation]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentStoreLocation]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentStoreLocation]['totalProfit'] += $itemProfit;
                    $reportData[$currentStoreLocation]['totalQuantity'] += $quantity;

                    $reportData[$currentStoreLocation]['items'][] = [
                        'product_name' => $item->product_name ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            foreach ($invoices as $invoice) {
                foreach ($invoice->items as $item) {
                    $currentStoreLocation = $item->store_location ? trim($item->store_location) : 'Unknown Location';
                    if (strtolower($currentStoreLocation) !== strtolower($storeLocation)) {
                        continue;
                    }

                    $costPrice = floatval($item->total_buying_cost ?? 0);
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->sales_price ?? $item->unit_price ?? 0);
                    $discount = floatval($item->discount_amount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $costPrice;
                    $itemSellingPrice = ($quantity * $unitPrice) - $specialDiscount ;
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentStoreLocation])) {
                        $reportData[$currentStoreLocation] = [
                            'storeLocation' => $currentStoreLocation,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentStoreLocation]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentStoreLocation]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentStoreLocation]['totalProfit'] += $itemProfit;
                    $reportData[$currentStoreLocation]['totalQuantity'] += $quantity;

                    $reportData[$currentStoreLocation]['items'][] = [
                        'product_name' => $item->description ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            $reportData = array_map(function ($item) {
                $profitPercentage = ($item['totalSellingPrice'] > 0) ? ($item['totalProfit'] / $item['totalSellingPrice']) * 100 : 0;
                return [
                    'storeLocation' => $item['storeLocation'],
                    'totalCostPrice' => number_format($item['totalCostPrice'], 2),
                    'totalSellingPrice' => number_format($item['totalSellingPrice'], 2),
                    'totalProfit' => number_format($item['totalProfit'], 2),
                    'totalQuantity' => number_format($item['totalQuantity'], 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    'items' => $item['items'],
                ];
            }, array_values($reportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'totalQuantityAll' => number_format($totalQuantityAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in getStoreLocationWiseProfitReport: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Failed to fetch store location report. Please try again later.',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function getCustomerWiseProfitReport(Request $request)
    {
        try {
            $customerName = trim($request->input('customerName'));
            if (empty($customerName)) {
                Log::warning('No customer name provided in getCustomerWiseProfitReport');
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            $customer = Customer::whereRaw('LOWER(customer_name) = ?', [strtolower($customerName)])->first();
            if (!$customer) {
                Log::info("Customer not found: {$customerName}");
                return response()->json([
                    'reportData' => [],
                    'summary' => [
                        'totalCostPriceAll' => '0.00',
                        'totalSellingPriceAll' => '0.00',
                        'totalProfitAll' => '0.00',
                        'totalQuantityAll' => '0.00',
                        'averageProfitPercentageAll' => '0.00%',
                    ],
                ], 200);
            }

            $salesQuery = Sale::with(['items.product'])
                ->select('id', 'created_at', 'customer_name', 'payment_type')
                ->whereRaw('LOWER(customer_name) = ?', [strtolower($customerName)]);

            $invoicesQuery = Invoice::with('items')
                ->select('id', 'invoice_date', 'customer_name', 'payment_method')
                ->whereRaw('LOWER(customer_name) = ?', [strtolower($customerName)]);

            if ($request->has('fromDate') && $request->has('toDate')) {
                $fromDate = $request->input('fromDate');
                $toDate = $request->input('toDate');
                if ($fromDate && $toDate) {
                    $salesQuery->whereBetween('created_at', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                    $invoicesQuery->whereBetween('invoice_date', [
                        $fromDate . ' 00:00:00',
                        $toDate . ' 23:59:59'
                    ]);
                }
            }

            if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '' && $request->input('paymentMethod') !== 'all') {
                $paymentMethod = trim(strtolower($request->input('paymentMethod')));
                $salesQuery->whereRaw('LOWER(payment_type) = ?', [$paymentMethod]);
                $invoicesQuery->whereRaw('LOWER(payment_method) = ?', [$paymentMethod]);
            }

            $sales = $salesQuery->get();
            $invoices = $invoicesQuery->get();

            $reportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;
            $totalQuantityAll = 0;

            foreach ($sales as $sale) {
                $currentCustomerName = $sale->customer_name ? trim($sale->customer_name) : 'Unknown Customer';
                if (strtolower($currentCustomerName) !== strtolower($customerName)) {
                    continue;
                }

                foreach ($sale->items as $item) {
                    $product = $item->product;
                    $buyingCost = $product ? floatval($product->buying_cost ?? 0) : 0;
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->unit_price ?? 0);
                    $discount = floatval($item->discount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $buyingCost * $quantity;
                    $itemSellingPrice = ($unitPrice * $quantity) - ($discount + $specialDiscount);
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentCustomerName])) {
                        $reportData[$currentCustomerName] = [
                            'customerName' => $currentCustomerName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentCustomerName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentCustomerName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentCustomerName]['totalProfit'] += $itemProfit;
                    $reportData[$currentCustomerName]['totalQuantity'] += $quantity;

                    $reportData[$currentCustomerName]['items'][] = [
                        'product_name' => $item->product_name ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            foreach ($invoices as $invoice) {
                $currentCustomerName = $invoice->customer_name ? trim($invoice->customer_name) : 'Unknown Customer';
                if (strtolower($currentCustomerName) !== strtolower($customerName)) {
                    continue;
                }

                foreach ($invoice->items as $item) {
                    $costPrice = floatval($item->total_buying_cost ?? 0);
                    $quantity = floatval($item->quantity ?? 0);
                    $unitPrice = floatval($item->sales_price ?? $item->unit_price ?? 0);
                    $discount = floatval($item->discount_amount ?? 0);
                    $specialDiscount = floatval($item->special_discount ?? 0);

                    $itemCostPrice = $costPrice;
                    $itemSellingPrice = ($quantity * $unitPrice) - $specialDiscount ;
                    $itemProfit = $itemSellingPrice - $itemCostPrice;

                    if (!isset($reportData[$currentCustomerName])) {
                        $reportData[$currentCustomerName] = [
                            'customerName' => $currentCustomerName,
                            'totalCostPrice' => 0,
                            'totalSellingPrice' => 0,
                            'totalProfit' => 0,
                            'totalQuantity' => 0,
                            'items' => [],
                        ];
                    }

                    $reportData[$currentCustomerName]['totalCostPrice'] += $itemCostPrice;
                    $reportData[$currentCustomerName]['totalSellingPrice'] += $itemSellingPrice;
                    $reportData[$currentCustomerName]['totalProfit'] += $itemProfit;
                    $reportData[$currentCustomerName]['totalQuantity'] += $quantity;

                    $reportData[$currentCustomerName]['items'][] = [
                        'product_name' => $item->description ?? 'Unknown Product',
                        'quantity' => number_format($quantity, 2),
                        'unit_price' => number_format($unitPrice, 2),
                        'total_cost' => number_format($itemCostPrice, 2),
                        'total_sales' => number_format($itemSellingPrice, 2),
                        'profit' => number_format($itemProfit, 2),
                    ];

                    $totalCostPriceAll += $itemCostPrice;
                    $totalSellingPriceAll += $itemSellingPrice;
                    $totalProfitAll += $itemProfit;
                    $totalQuantityAll += $quantity;
                }
            }

            $reportData = array_map(function ($item) {
                $profitPercentage = ($item['totalSellingPrice'] > 0) ? ($item['totalProfit'] / $item['totalSellingPrice']) * 100 : 0;
                return [
                    'customerName' => $item['customerName'],
                    'totalCostPrice' => number_format($item['totalCostPrice'], 2),
                    'totalSellingPrice' => number_format($item['totalSellingPrice'], 2),
                    'totalProfit' => number_format($item['totalProfit'], 2),
                    'totalQuantity' => number_format($item['totalQuantity'], 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    'items' => $item['items'],
                ];
            }, array_values($reportData));

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'totalQuantityAll' => number_format($totalQuantityAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in getCustomerWiseProfitReport: ' . $e->getMessage(), [
                'exception' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'error' => 'Failed to fetch customer report. Please try again later.',
                'details' => $e->getMessage()
            ], 500);
        }
    }

    public function getDeletedSales()
    {
        $sales = Sale::onlyTrashed()->with(['itemsWithTrashed.product' => function($query) {
            $query->withTrashed();
        }, 'deletedByUser'])->get();
        // Transform the sales to include items in the expected format and deleted_by_user
        $transformedSales = $sales->map(function($sale) {
            $saleData = $sale->toArray();
            $saleData['items'] = $sale->itemsWithTrashed->map(function($item) {
                return [
                    'id' => $item->id,
                    'sale_id' => $item->sale_id,
                    'product_id' => $item->product_id,
                    'product_name' => $item->product_name,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'discount' => $item->discount,
                    'special_discount' => $item->special_discount,
                    'total' => $item->total,
                    'batch_number' => $item->batch_number,
                    'expiry_date' => $item->expiry_date,
                    'product' => $item->product ? [
                        'id' => $item->product->id,
                        'product_name' => $item->product->product_name,
                        'item_code' => $item->product->item_code,
                    ] : null,
                    'created_at' => $item->created_at,
                    'updated_at' => $item->updated_at,
                    'deleted_at' => $item->deleted_at,
                ];
            })->toArray();
            $saleData['deleted_by_user'] = $sale->deletedByUser ? [
                'id' => $sale->deletedByUser->id,
                'name' => $sale->deletedByUser->name,
                'email' => $sale->deletedByUser->email,
            ] : null;
            return $saleData;
        });
        return response()->json([
            'data' => $transformedSales,
            'meta' => [
                'current_page' => 1,
                'last_page' => 1,
                'per_page' => $sales->count(),
                'total' => $sales->count(),
            ]
        ]);
    }

    public function restoreSale($id)
    {
        try {
            DB::beginTransaction();
            
            // Restore the main sale
            $sale = Sale::onlyTrashed()->findOrFail($id);
            $sale->restore();
            
            // Restore all associated sale items
            $sale->itemsWithTrashed()->restore();
            
            // Log the restore activity
            \App\Http\Controllers\UserActivityController::logActivity(
                auth()->id() ?? $request->user()->id ?? 1,
                'Sale Restored',
                'Sales',
                $sale->id,
                [
                    'bill_number' => $sale->bill_number,
                    'customer_name' => $sale->customer_name,
                    'total_amount' => $sale->total,
                    'restored_by' => auth()->id() ?? $request->user()->id ?? 1
                ]
            );
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Sale and all items restored successfully',
                'data' => $sale->load(['items', 'customer'])
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error restoring sale: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore sale: ' . $e->getMessage()
            ], 500);
        }
    }

    public function forceDeleteSale($id)
    {
        try {
            DB::beginTransaction();
            
            $sale = Sale::onlyTrashed()->findOrFail($id);
            
            // Force delete all associated sale items
            $sale->itemsWithTrashed()->forceDelete();
            
            // Log the force delete activity
            \App\Http\Controllers\UserActivityController::logActivity(
                auth()->id() ?? $request->user()->id ?? 1,
                'Sale Permanently Deleted',
                'Sales',
                $sale->id,
                [
                    'bill_number' => $sale->bill_number,
                    'customer_name' => $sale->customer_name,
                    'total_amount' => $sale->total,
                    'permanently_deleted_by' => auth()->id() ?? $request->user()->id ?? 1
                ]
            );
            
            // Force delete the sale
            $sale->forceDelete();
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Sale and all items permanently deleted'
            ]);
            
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Error force deleting sale: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to permanently delete sale: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getSaleItems($id)
    {
        try {
            $sale = Sale::withTrashed()->findOrFail($id);
            
            // Get sale items with product information (including trashed products)
            $items = $sale->itemsWithTrashed()->with(['product' => function($query) {
                $query->withTrashed();
            }])->get();
            
            return response()->json([
                'success' => true,
                'items' => $items
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching sale items: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch sale items'
            ], 500);
        }
    }

    /**
     * View a bill by bill number
     * This endpoint serves bill data that can be viewed or downloaded
     */
    public function viewBill($billNumber)
    {
        try {
            Log::info('Fetching bill with number: ' . $billNumber);

            // First try to find in sales table
            $sale = Sale::with(['items.product.variants', 'customer'])
                ->where('bill_number', $billNumber)
                ->first();

            if ($sale) {
                // Enhance items with variant data
                $sale->items->each(function ($item) {
                    if ($item->product && $item->product->variants->count() > 0) {
                        $variant = $item->product->variants->first();
                        $item->sales_price = $variant->sales_price;
                        $item->variant_mrp = $variant->mrp;
                    }
                });

                // Format the response for bill viewing
                $billData = [
                    'id' => $sale->id,
                    'bill_number' => $sale->bill_number,
                    'customer_name' => $sale->customer_name,
                    'customer_mobile' => $sale->customer_mobile,
                    'bill_date' => $sale->created_at->format('Y-m-d'),
                    'bill_time' => $sale->created_at->format('h:i:s A'),
                    'subtotal' => $sale->subtotal,
                    'discount' => $sale->discount,
                    'tax' => $sale->tax,
                    'shipping' => $sale->shipping ?? 0,
                    'total' => $sale->total,
                    'payment_type' => $sale->payment_type,
                    'received_amount' => $sale->received_amount,
                    'balance_amount' => $sale->balance_amount,
                    'items' => $sale->items,
                    'cashier_name' => $sale->user_name ?? 'System',
                    'type' => 'sale'
                ];

                return response()->json([
                    'success' => true,
                    'data' => $billData
                ]);
            }

            // If not found in sales, try invoices table
            $invoice = Invoice::with(['items.productVariant', 'customer'])
                ->where('invoice_no', $billNumber)
                ->first();

            if ($invoice) {
                $billData = [
                    'id' => $invoice->id,
                    'bill_number' => $invoice->invoice_no,
                    'customer_name' => $invoice->customer_name,
                    'customer_mobile' => $invoice->customer_mobile,
                    'bill_date' => $invoice->invoice_date->format('Y-m-d'),
                    'bill_time' => $invoice->invoice_time ?? $invoice->invoice_date->format('h:i:s A'),
                    'subtotal' => $invoice->subtotal,
                    'discount' => $invoice->discount,
                    'tax' => $invoice->tax,
                    'shipping' => $invoice->shipping ?? 0,
                    'total' => $invoice->total,
                    'payment_type' => $invoice->payment_type,
                    'received_amount' => $invoice->purchase_amount,
                    'balance_amount' => $invoice->balance,
                    'items' => $invoice->items,
                    'cashier_name' => $invoice->user_name ?? 'System',
                    'type' => 'invoice'
                ];

                return response()->json([
                    'success' => true,
                    'data' => $billData
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Bill not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('Error fetching bill: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bill: ' . $e->getMessage()
            ], 500);
        }
    }
}